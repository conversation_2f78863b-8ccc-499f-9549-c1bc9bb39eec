import { emit, on, once, EventHandler } from '@create-figma-plugin/utilities'

/**
 * Type for event handler functions
 */
type EventHandlerFunction = (...args: any[]) => void

/**
 * Type for cleanup functions returned by event listeners
 */
type CleanupFunction = () => void

/**
 * Interface for event listener management
 */
interface EventListeners {
  [eventName: string]: {
    handler: EventHandlerFunction
    cleanup: CleanupFunction
  }[]
}

/**
 * Channel class provides a unified interface for bidirectional communication
 * between the main thread and UI thread in Figma plugins.
 *
 * This class wraps create-figma-plugin's utilities (on, once, emit) to provide:
 * - Simplified API for sending and receiving messages
 * - Proper TypeScript typing
 * - Event listener management and cleanup
 * - Support for both one-time and persistent listeners
 */
export class Channel {
  private listeners: EventListeners = {}

  /**
   * Send a message to the other thread (main <-> UI)
   *
   * @param eventName - The name of the event to emit
   * @param data - Optional data to send with the event
   */
  send<T extends EventHandler>(eventName: T['name'], ...data: Parameters<T['handler']>): void {
    emit<T>(eventName, ...data)
  }

  /**
   * Listen for messages from the other thread
   *
   * @param eventName - The name of the event to listen for
   * @param handler - The function to call when the event is received
   * @returns A function to remove this listener
   */
  on<T extends EventHandler>(eventName: T['name'], handler: T['handler']): CleanupFunction {
    const cleanup = on<T>(eventName, handler)

    // Store the listener for cleanup management
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = []
    }

    this.listeners[eventName].push({
      handler: handler as EventHandlerFunction,
      cleanup
    })

    // Return a cleanup function that also removes from our tracking
    return () => {
      cleanup()
      this.removeListenerFromTracking(eventName, handler as EventHandlerFunction)
    }
  }

  /**
   * Listen for a single message from the other thread (auto-removes after first call)
   *
   * @param eventName - The name of the event to listen for
   * @param handler - The function to call when the event is received
   * @returns A function to remove this listener before it fires
   */
  once<T extends EventHandler>(eventName: T['name'], handler: T['handler']): CleanupFunction {
    const cleanup = once<T>(eventName, handler)

    // Store the listener for cleanup management
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = []
    }

    const listenerEntry = {
      handler: handler as EventHandlerFunction,
      cleanup
    }

    this.listeners[eventName].push(listenerEntry)

    // Return a cleanup function that also removes from our tracking
    return () => {
      cleanup()
      this.removeListenerFromTracking(eventName, handler as EventHandlerFunction)
    }
  }

  /**
   * Remove a specific event listener
   *
   * @param eventName - The name of the event
   * @param handler - The handler function to remove (optional - if not provided, removes all listeners for the event)
   */
  off<T extends EventHandler>(eventName: T['name'], handler?: T['handler']): void {
    if (!this.listeners[eventName]) {
      return
    }

    if (handler) {
      // Remove specific handler
      const listenerIndex = this.listeners[eventName].findIndex(
        listener => listener.handler === handler
      )

      if (listenerIndex !== -1) {
        const listener = this.listeners[eventName][listenerIndex]
        listener.cleanup()
        this.listeners[eventName].splice(listenerIndex, 1)

        // Clean up empty event arrays
        if (this.listeners[eventName].length === 0) {
          delete this.listeners[eventName]
        }
      }
    } else {
      // Remove all handlers for this event
      this.listeners[eventName].forEach(listener => listener.cleanup())
      delete this.listeners[eventName]
    }
  }

  /**
   * Remove all event listeners managed by this channel
   */
  removeAllListeners(): void {
    Object.keys(this.listeners).forEach(eventName => {
      this.listeners[eventName].forEach(listener => listener.cleanup())
    })
    this.listeners = {}
  }

  /**
   * Get the number of active listeners for a specific event
   *
   * @param eventName - The name of the event
   * @returns The number of active listeners
   */
  getListenerCount(eventName: string): number {
    return this.listeners[eventName]?.length || 0
  }

  /**
   * Get all active event names that have listeners
   *
   * @returns Array of event names with active listeners
   */
  getActiveEvents(): string[] {
    return Object.keys(this.listeners)
  }

  /**
   * Private helper to remove a listener from our tracking
   */
  private removeListenerFromTracking(eventName: string, handler: EventHandlerFunction): void {
    if (!this.listeners[eventName]) {
      return
    }

    const listenerIndex = this.listeners[eventName].findIndex(
      listener => listener.handler === handler
    )

    if (listenerIndex !== -1) {
      this.listeners[eventName].splice(listenerIndex, 1)

      // Clean up empty event arrays
      if (this.listeners[eventName].length === 0) {
        delete this.listeners[eventName]
      }
    }
  }
}

/**
 * Create a new Channel instance
 *
 * @returns A new Channel instance for bidirectional communication
 */
export function createChannel(): Channel {
  return new Channel()
}