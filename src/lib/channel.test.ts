/**
 * Basic tests for the Channel class
 * Note: These are simple unit tests to verify the Channel API works correctly.
 * Full integration testing would require a Figma plugin environment.
 */

import { EventHandler } from "@create-figma-plugin/utilities";
import { Channel, createChannel } from "./channel";

// Mock the create-figma-plugin utilities
jest.mock("@create-figma-plugin/utilities", () => ({
	emit: jest.fn(),
	on: jest.fn(() => jest.fn()), // Returns cleanup function
	once: jest.fn(() => jest.fn()), // Returns cleanup function
}));

// Test event handler interfaces
interface TestEventHandler extends EventHandler {
	name: "TEST_EVENT";
	handler: (data: string) => void;
}

interface TestEventHandler2 extends EventHandler {
	name: "TEST_EVENT_2";
	handler: (count: number, message: string) => void;
}

describe("Channel", () => {
	let channel: Channel;
	const mockEmit = require("@create-figma-plugin/utilities").emit;
	const mockOn = require("@create-figma-plugin/utilities").on;
	const mockOnce = require("@create-figma-plugin/utilities").once;

	beforeEach(() => {
		channel = new Channel();
		jest.clearAllMocks();
	});

	describe("createChannel", () => {
		it("should create a new Channel instance", () => {
			const newChannel = createChannel();
			expect(newChannel).toBeInstanceOf(Channel);
		});
	});

	describe("send", () => {
		it("should call emit with correct parameters", () => {
			channel.send<TestEventHandler>("TEST_EVENT", "hello");
			expect(mockEmit).toHaveBeenCalledWith("TEST_EVENT", "hello");
		});

		it("should call emit with multiple parameters", () => {
			channel.send<TestEventHandler2>("TEST_EVENT_2", 42, "world");
			expect(mockEmit).toHaveBeenCalledWith("TEST_EVENT_2", 42, "world");
		});
	});

	describe("on", () => {
		it("should call on with correct parameters", () => {
			const handler = jest.fn();
			const cleanup = channel.on<TestEventHandler>("TEST_EVENT", handler);

			expect(mockOn).toHaveBeenCalledWith("TEST_EVENT", handler);
			expect(typeof cleanup).toBe("function");
		});

		it("should track listeners for cleanup", () => {
			const handler = jest.fn();
			channel.on<TestEventHandler>("TEST_EVENT", handler);

			expect(channel.getListenerCount("TEST_EVENT")).toBe(1);
			expect(channel.getActiveEvents()).toContain("TEST_EVENT");
		});
	});

	describe("once", () => {
		it("should call once with correct parameters", () => {
			const handler = jest.fn();
			const cleanup = channel.once<TestEventHandler>("TEST_EVENT", handler);

			expect(mockOnce).toHaveBeenCalledWith("TEST_EVENT", handler);
			expect(typeof cleanup).toBe("function");
		});

		it("should track listeners for cleanup", () => {
			const handler = jest.fn();
			channel.once<TestEventHandler>("TEST_EVENT", handler);

			expect(channel.getListenerCount("TEST_EVENT")).toBe(1);
		});
	});

	describe("off", () => {
		it("should remove specific listener", () => {
			const handler = jest.fn();
			const mockCleanup = jest.fn();
			mockOn.mockReturnValue(mockCleanup);

			channel.on<TestEventHandler>("TEST_EVENT", handler);
			expect(channel.getListenerCount("TEST_EVENT")).toBe(1);

			channel.off<TestEventHandler>("TEST_EVENT", handler);
			expect(mockCleanup).toHaveBeenCalled();
			expect(channel.getListenerCount("TEST_EVENT")).toBe(0);
		});

		it("should remove all listeners for an event when no handler specified", () => {
			const handler1 = jest.fn();
			const handler2 = jest.fn();
			const mockCleanup1 = jest.fn();
			const mockCleanup2 = jest.fn();

			mockOn
				.mockReturnValueOnce(mockCleanup1)
				.mockReturnValueOnce(mockCleanup2);

			channel.on<TestEventHandler>("TEST_EVENT", handler1);
			channel.on<TestEventHandler>("TEST_EVENT", handler2);
			expect(channel.getListenerCount("TEST_EVENT")).toBe(2);

			channel.off<TestEventHandler>("TEST_EVENT");
			expect(mockCleanup1).toHaveBeenCalled();
			expect(mockCleanup2).toHaveBeenCalled();
			expect(channel.getListenerCount("TEST_EVENT")).toBe(0);
		});
	});

	describe("removeAllListeners", () => {
		it("should remove all listeners from all events", () => {
			const handler1 = jest.fn();
			const handler2 = jest.fn();
			const mockCleanup1 = jest.fn();
			const mockCleanup2 = jest.fn();

			mockOn.mockReturnValueOnce(mockCleanup1);
			mockOnce.mockReturnValueOnce(mockCleanup2);

			channel.on<TestEventHandler>("TEST_EVENT", handler1);
			channel.once<TestEventHandler2>("TEST_EVENT_2", handler2);

			expect(channel.getActiveEvents()).toHaveLength(2);

			channel.removeAllListeners();

			expect(mockCleanup1).toHaveBeenCalled();
			expect(mockCleanup2).toHaveBeenCalled();
			expect(channel.getActiveEvents()).toHaveLength(0);
		});
	});

	describe("getListenerCount", () => {
		it("should return 0 for non-existent events", () => {
			expect(channel.getListenerCount("NON_EXISTENT")).toBe(0);
		});

		it("should return correct count for existing events", () => {
			const handler1 = jest.fn();
			const handler2 = jest.fn();

			channel.on<TestEventHandler>("TEST_EVENT", handler1);
			channel.on<TestEventHandler>("TEST_EVENT", handler2);

			expect(channel.getListenerCount("TEST_EVENT")).toBe(2);
		});
	});

	describe("getActiveEvents", () => {
		it("should return empty array when no listeners", () => {
			expect(channel.getActiveEvents()).toEqual([]);
		});

		it("should return array of event names with listeners", () => {
			const handler1 = jest.fn();
			const handler2 = jest.fn();

			channel.on<TestEventHandler>("TEST_EVENT", handler1);
			channel.once<TestEventHandler2>("TEST_EVENT_2", handler2);

			const activeEvents = channel.getActiveEvents();
			expect(activeEvents).toContain("TEST_EVENT");
			expect(activeEvents).toContain("TEST_EVENT_2");
			expect(activeEvents).toHaveLength(2);
		});
	});
});
